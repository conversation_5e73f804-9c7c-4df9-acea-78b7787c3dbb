<template>
  <view class="template-selector">
    <!-- 触发按钮 -->
    <NavItem
      title="选择模版"
      :value="selectedTemplate ? selectedTemplate.Name : ''"
      :placeholder="placeholder"
      @click="openTemplateSelector"
    />

    <!-- ActionSheet 弹出菜单 -->
    <u-action-sheet
      :show="showActionSheet"
      @close="closeActionSheet"
      cancelText="取消"
      round="true"
      :safeAreaInsetBottom="true"
      closeOnClickOverlay="true"
    >
      <view class="action-sheet-container">
        <!-- 标题区域 -->
        <view class="action-sheet-header">
          <view class="header-content">
            <text class="header-title">选择模板</text>
            <text class="header-subtitle">共 {{ templates.length }} 个模板</text>
          </view>
          <view class="header-divider"></view>
        </view>

        <!-- 内容区域 -->
        <view class="custom-content" v-if="templates.length > 0">
          <scroll-view class="template-scroll" scroll-y="true" :show-scrollbar="false">
            <view class="template-list">
              <view
                class="template-item"
                v-for="(template, index) in templates"
                :key="template.TemplateId"
                @click="selectTemplate(template, index)"
                :class="{ 'selected': selectedTemplate && selectedTemplate.TemplateId === template.TemplateId }"
              >
                <view class="template-image-container">
                  <image
                    class="template-image"
                    :src="template.imageAddress"
                    mode="aspectFill"
                  />
                  <view class="image-overlay" v-if="selectedTemplate && selectedTemplate.TemplateId === template.TemplateId">
                    <text class="check-icon">✓</text>
                  </view>
                </view>
                <view class="template-info">
                  <text class="template-name">{{ template.Name }}</text>
                  <text class="template-desc">{{ template.Status }}</text>
                  <view class="template-meta">
                    <text class="template-source">{{ template.CreateSource }}</text>
                    <text class="template-time">{{ formatTime(template.CreationTime) }}</text>
                  </view>
                </view>
              </view>
            </view>
          </scroll-view>
        </view>
        <view v-else class="empty-template">
          <view class="empty-icon">📋</view>
          <text class="empty-text">暂无模板数据</text>
        </view>
      </view>
    </u-action-sheet>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import NavItem from "@/components/platform/nav-item.vue";
import { listTemplates } from "@/api/platform/template";

// 定义属性
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => null,
  },
  placeholder: {
    type: String,
    default: "点击选择模版",
  },
});

// 定义事件
const emit = defineEmits(["update:modelValue"]);

// 数据状态
const showActionSheet = ref(false);
const templates = ref([]);
const selectedTemplate = computed(() => props.modelValue);

// 获取模板列表
const getTemplateList = async () => {
  try {
    const params = {
      pageNum: 1,
      pageSize: 999,
    };
    const res = await listTemplates(params);
    console.log("res", res);
    if (res.code === 200) {
      templates.value = res.data.Templates || [];
    }
    console.log("templates", templates);
  } catch (err) {
    console.error("获取模板列表失败:", err);
  }
};

// 打开模板选择器
const openTemplateSelector = () => {
  getTemplateList();
  showActionSheet.value = true;
};

// 关闭模板选择器
const closeActionSheet = () => {
  showActionSheet.value = false;
};

// 直接选择模板（通过点击列表项）
const selectTemplate = (template) => {
  emit("update:modelValue", template);
  closeActionSheet();
};

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return '';
  try {
    const date = new Date(timeStr);
    const now = new Date();
    const diff = now - date;
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (days === 0) return '今天';
    if (days === 1) return '昨天';
    if (days < 7) return `${days}天前`;
    if (days < 30) return `${Math.floor(days / 7)}周前`;
    if (days < 365) return `${Math.floor(days / 30)}个月前`;
    return `${Math.floor(days / 365)}年前`;
  } catch (e) {
    return timeStr;
  }
};

// 组件挂载时获取模板列表
onMounted(() => {
  getTemplateList();
});
</script>

<style lang="scss" scoped>
.template-selector {
  .action-sheet-container {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 24rpx 24rpx 0 0;
    overflow: hidden;

    /*  #ifdef  MP-WEIXIN  */
    background: linear-gradient(135deg, #1a1a1a 0%, #2c2c2c 100%);
    /*  #endif  */
  }

  .action-sheet-header {
    padding: 32rpx 32rpx 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20rpx);

    /*  #ifdef  MP-WEIXIN  */
    background: rgba(42, 42, 42, 0.95);
    /*  #endif  */

    .header-content {
      text-align: center;
      margin-bottom: 24rpx;

      .header-title {
        display: block;
        font-size: 36rpx;
        font-weight: 600;
        color: #1a1a1a;
        margin-bottom: 8rpx;

        /*  #ifdef  MP-WEIXIN  */
        color: #ffffff;
        /*  #endif  */
      }

      .header-subtitle {
        display: block;
        font-size: 24rpx;
        color: #666;

        /*  #ifdef  MP-WEIXIN  */
        color: #aaa;
        /*  #endif  */
      }
    }

    .header-divider {
      height: 4rpx;
      background: linear-gradient(90deg, transparent 0%, #007aff 50%, transparent 100%);
      border-radius: 2rpx;
      margin: 0 auto;
      width: 120rpx;
    }
  }

  .custom-content {
    padding: 24rpx 0 0;
    max-height: 65vh;

    .template-scroll {
      max-height: 60vh;

      .template-list {
        padding: 0 24rpx 24rpx;
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(320rpx, 1fr));
        gap: 24rpx;

        .template-item {
          position: relative;
          display: flex;
          flex-direction: column;
          padding: 24rpx;
          border-radius: 20rpx;
          background: rgba(255, 255, 255, 0.9);
          backdrop-filter: blur(10rpx);
          border: 2rpx solid rgba(255, 255, 255, 0.2);
          box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
          transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          overflow: hidden;

          /*  #ifdef  MP-WEIXIN  */
          background: rgba(45, 45, 45, 0.9);
          border: 2rpx solid rgba(255, 255, 255, 0.1);
          box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
          /*  #endif  */

          &:active {
            transform: translateY(-6rpx) scale(0.98);
            box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);

            /*  #ifdef  MP-WEIXIN  */
            box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.4);
            /*  #endif  */
          }

          &.selected {
            border-color: #007aff;
            box-shadow: 0 8rpx 32rpx rgba(0, 122, 255, 0.25);
            transform: translateY(-2rpx);

            /*  #ifdef  MP-WEIXIN  */
            border-color: #007aff;
            box-shadow: 0 8rpx 32rpx rgba(0, 122, 255, 0.4);
            /*  #endif  */
          }

          .template-image-container {
            position: relative;
            width: 100%;
            margin-bottom: 20rpx;

            .template-image {
              width: 100%;
              height: 220rpx;
              border-radius: 16rpx;
              background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
              object-fit: cover;
              transition: all 0.3s ease;

              /*  #ifdef  MP-WEIXIN  */
              background: linear-gradient(135deg, #434343 0%, #000000 100%);
              /*  #endif  */
            }

            .image-overlay {
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: rgba(0, 122, 255, 0.8);
              border-radius: 16rpx;
              display: flex;
              align-items: center;
              justify-content: center;
              animation: fadeIn 0.3s ease;

              .check-icon {
                font-size: 48rpx;
                color: #ffffff;
                font-weight: bold;
              }
            }
          }

          .template-info {
            flex: 1;
            display: flex;
            flex-direction: column;

            .template-name {
              font-size: 30rpx;
              font-weight: 600;
              color: #1a1a1a;
              display: block;
              margin-bottom: 12rpx;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              line-height: 1.3;

              /*  #ifdef  MP-WEIXIN  */
              color: #ffffff;
              /*  #endif  */
            }

            .template-desc {
              font-size: 26rpx;
              color: #666;
              margin-bottom: 16rpx;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              padding: 6rpx 12rpx;
              background: rgba(0, 122, 255, 0.1);
              border-radius: 12rpx;
              text-align: center;
              font-weight: 500;

              /*  #ifdef  MP-WEIXIN  */
              color: #bbb;
              background: rgba(0, 122, 255, 0.2);
              /*  #endif  */
            }

            .template-meta {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-top: auto;

              .template-source,
              .template-time {
                font-size: 22rpx;
                color: #999;
                line-height: 1.4;
                flex: 1;

                /*  #ifdef  MP-WEIXIN  */
                color: #888;
                /*  #endif  */
              }

              .template-time {
                text-align: right;
                font-weight: 500;
              }
            }
          }
        }
      }
    }
  }

  .empty-template {
    padding: 80rpx 40rpx;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400rpx;

    .empty-icon {
      font-size: 120rpx;
      margin-bottom: 24rpx;
      opacity: 0.6;
      animation: bounce 2s infinite;
    }

    .empty-text {
      font-size: 32rpx;
      font-weight: 600;
      color: #666;
      margin-bottom: 12rpx;

      /*  #ifdef  MP-WEIXIN  */
      color: #aaa;
      /*  #endif  */
    }

    .empty-desc {
      font-size: 24rpx;
      color: #999;
      line-height: 1.5;

      /*  #ifdef  MP-WEIXIN  */
      color: #888;
      /*  #endif  */
    }
  }
}

// 动画效果
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10rpx);
  }
  60% {
    transform: translateY(-5rpx);
  }
}
</style>
